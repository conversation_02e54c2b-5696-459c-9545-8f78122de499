import { supabase } from '@/lib/supabase'
import { BlogPost, Project } from '@/types'

// Convert Supabase blog post to frontend BlogPost format
function convertSupabaseBlogPost(data: any): BlogPost {
  return {
    slug: data.slug,
    title: data.title,
    excerpt: data.excerpt || '',
    date: data.created_at,
    featuredImage: data.featured_image || '/images/blog/default.png',
    content: data.content,
    readTime: data.reading_time || 5,
    tags: data.tags || [],
    author: '<PERSON>',
    categories: [],
  }
}

// Convert Supabase project to frontend Project format
function convertSupabaseProject(data: any): Project {
  return {
    slug: data.slug,
    title: data.title,
    description: data.description || '',
    featuredImage: data.featured_image || '/images/projects/default.jpg',
    images: [],
    technologies: data.tech_stack || [],
    liveUrl: data.project_url,
    githubUrl: data.github_url,
    content: data.content,
    date: data.created_at,
    category: 'Web Development',
    client: undefined,
    industry: undefined,
    challenge: undefined,
    strategy: undefined,
  }
}

// Get all published blog posts from Supabase
export async function getBlogPostsFromSupabase(): Promise<BlogPost[]> {
  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching blog posts:', error)
      return []
    }

    return data.map(convertSupabaseBlogPost)
  } catch (error) {
    console.error('Error in getBlogPostsFromSupabase:', error)
    return []
  }
}

// Get a single blog post by slug from Supabase
export async function getBlogPostFromSupabase(slug: string): Promise<BlogPost | null> {
  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single()

    if (error) {
      console.error('Error fetching blog post:', error)
      return null
    }

    return convertSupabaseBlogPost(data)
  } catch (error) {
    console.error('Error in getBlogPostFromSupabase:', error)
    return null
  }
}

// Get all published projects from Supabase
export async function getProjectsFromSupabase(): Promise<Project[]> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching projects:', error)
      return []
    }

    return data.map(convertSupabaseProject)
  } catch (error) {
    console.error('Error in getProjectsFromSupabase:', error)
    return []
  }
}

// Get a single project by slug from Supabase
export async function getProjectFromSupabase(slug: string): Promise<Project | null> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single()

    if (error) {
      console.error('Error fetching project:', error)
      return null
    }

    return convertSupabaseProject(data)
  } catch (error) {
    console.error('Error in getProjectFromSupabase:', error)
    return null
  }
}

// Get adjacent blog posts for navigation
export async function getAdjacentPostsFromSupabase(currentSlug: string): Promise<{
  previousPost: BlogPost | null;
  nextPost: BlogPost | null;
}> {
  try {
    const allPosts = await getBlogPostsFromSupabase()
    const currentIndex = allPosts.findIndex(post => post.slug === currentSlug)

    return {
      previousPost: currentIndex > 0 ? allPosts[currentIndex - 1] : null,
      nextPost: currentIndex < allPosts.length - 1 ? allPosts[currentIndex + 1] : null,
    }
  } catch (error) {
    console.error('Error in getAdjacentPostsFromSupabase:', error)
    return {
      previousPost: null,
      nextPost: null,
    }
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    // Try to get posts from Supabase first
    const supabasePosts = await getBlogPostsFromSupabase()
    
    if (supabasePosts.length > 0) {
      return supabasePosts
    }

    // Fallback to markdown if no Supabase posts
    const { getBlogPosts: getMarkdownPosts } = await import('./markdown')
    return await getMarkdownPosts()
  } catch (error) {
    console.error('Error in hybrid getBlogPosts:', error)
    return []
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    // Try to get post from Supabase first
    const supabasePost = await getBlogPostFromSupabase(slug)
    
    if (supabasePost) {
      return supabasePost
    }

    // Fallback to markdown if not found in Supabase
    const { getBlogPost: getMarkdownPost } = await import('./markdown')
    return await getMarkdownPost(slug)
  } catch (error) {
    console.error('Error in hybrid getBlogPost:', error)
    return null
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getProjects(): Promise<Project[]> {
  try {
    // Try to get projects from Supabase first
    const supabaseProjects = await getProjectsFromSupabase()
    
    if (supabaseProjects.length > 0) {
      return supabaseProjects
    }

    // Fallback to markdown if no Supabase projects
    const { getProjects: getMarkdownProjects } = await import('./markdown')
    return await getMarkdownProjects()
  } catch (error) {
    console.error('Error in hybrid getProjects:', error)
    return []
  }
}

// Hybrid function that tries Supabase first, then falls back to markdown
export async function getProject(slug: string): Promise<Project | null> {
  try {
    // Try to get project from Supabase first
    const supabaseProject = await getProjectFromSupabase(slug)
    
    if (supabaseProject) {
      return supabaseProject
    }

    // Fallback to markdown if not found in Supabase
    const { getProject: getMarkdownProject } = await import('./markdown')
    return await getMarkdownProject(slug)
  } catch (error) {
    console.error('Error in hybrid getProject:', error)
    return null
  }
}

// Hybrid function for adjacent posts
export async function getAdjacentPosts(currentSlug: string): Promise<{
  previousPost: BlogPost | null;
  nextPost: BlogPost | null;
}> {
  try {
    // Try Supabase first
    const supabasePosts = await getBlogPostsFromSupabase()
    
    if (supabasePosts.length > 0) {
      return await getAdjacentPostsFromSupabase(currentSlug)
    }

    // Fallback to markdown
    const { getAdjacentPosts: getMarkdownAdjacentPosts } = await import('./markdown')
    return await getMarkdownAdjacentPosts(currentSlug)
  } catch (error) {
    console.error('Error in hybrid getAdjacentPosts:', error)
    return {
      previousPost: null,
      nextPost: null,
    }
  }
}

'use client'

import { useAuth } from '@/components/providers/AuthProvider'
import { supabase } from '@/lib/supabase'
import { useState, useEffect } from 'react'

export default function DebugAuthPage() {
  const { user, session, loading } = useAuth()
  const [supabaseSession, setSupabaseSession] = useState<any>(null)

  useEffect(() => {
    const checkSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      setSupabaseSession({ session, error })
    }
    checkSession()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Authentication Debug
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Auth Provider State */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Auth Provider State
            </h2>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Loading:</span> {loading ? 'true' : 'false'}
              </div>
              <div>
                <span className="font-medium">User:</span> {user ? 'Authenticated' : 'Not authenticated'}
              </div>
              <div>
                <span className="font-medium">Session:</span> {session ? 'Active' : 'None'}
              </div>
              {user && (
                <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded">
                  <div><span className="font-medium">Email:</span> {user.email}</div>
                  <div><span className="font-medium">ID:</span> {user.id}</div>
                  <div><span className="font-medium">Full Name:</span> {user.user_metadata?.full_name || 'Not set'}</div>
                </div>
              )}
            </div>
          </div>

          {/* Direct Supabase State */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Direct Supabase State
            </h2>
            <div className="space-y-2 text-sm">
              {supabaseSession ? (
                <>
                  <div>
                    <span className="font-medium">Session:</span> {supabaseSession.session ? 'Active' : 'None'}
                  </div>
                  <div>
                    <span className="font-medium">Error:</span> {supabaseSession.error ? supabaseSession.error.message : 'None'}
                  </div>
                  {supabaseSession.session && (
                    <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded">
                      <div><span className="font-medium">User ID:</span> {supabaseSession.session.user?.id}</div>
                      <div><span className="font-medium">Email:</span> {supabaseSession.session.user?.email}</div>
                      <div><span className="font-medium">Expires:</span> {new Date(supabaseSession.session.expires_at * 1000).toLocaleString()}</div>
                    </div>
                  )}
                </>
              ) : (
                <div>Loading...</div>
              )}
            </div>
          </div>

          {/* Environment Variables */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Environment Check
            </h2>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Supabase URL:</span> {process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Missing'}
              </div>
              <div>
                <span className="font-medium">Supabase Anon Key:</span> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Missing'}
              </div>
              <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                <div>URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}</div>
                <div>Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...</div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h2>
            <div className="space-y-3">
              <button
                onClick={() => window.location.href = '/signup'}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Go to Signup
              </button>
              <button
                onClick={() => window.location.href = '/login'}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Go to Login
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Go to Dashboard
              </button>
              {user && (
                <button
                  onClick={async () => {
                    await supabase.auth.signOut()
                    window.location.reload()
                  }}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Sign Out
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

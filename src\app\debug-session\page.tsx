'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'

export default function DebugSessionPage() {
  const [sessionInfo, setSessionInfo] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkSession()
  }, [])

  const checkSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      setSessionInfo({
        session: session ? {
          user: {
            id: session.user.id,
            email: session.user.email,
            role: session.user.role
          },
          expires_at: session.expires_at,
          access_token: session.access_token ? session.access_token.substring(0, 20) + '...' : null
        } : null,
        error: error?.message
      })
    } catch (error: any) {
      setSessionInfo({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const login = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>', // Replace with your email
        password: 'password123' // Replace with your password
      })
      
      if (error) throw error
      
      await checkSession()
    } catch (error: any) {
      setSessionInfo({ error: error.message })
    }
  }

  const logout = async () => {
    try {
      await supabase.auth.signOut()
      await checkSession()
    } catch (error: any) {
      setSessionInfo({ error: error.message })
    }
  }

  if (loading) {
    return <div className="p-8">Loading...</div>
  }

  return (
    <div className="p-8 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Session Debug</h1>
        <p className="text-gray-600">Check your authentication status</p>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="space-x-4 mb-4">
          <button
            onClick={checkSession}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Check Session
          </button>
          
          <button
            onClick={login}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Login
          </button>
          
          <button
            onClick={logout}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Logout
          </button>
        </div>
        
        {sessionInfo && (
          <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">
            <pre className="text-sm overflow-auto">{JSON.stringify(sessionInfo, null, 2)}</pre>
          </div>
        )}
      </div>
      
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Instructions:</h3>
        <ol className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>1. Click "Check Session" to see your current authentication status</li>
          <li>2. If no session, update the login credentials in the code and click "Test Login"</li>
          <li>3. Once logged in, you can test the upload functionality</li>
        </ol>
      </div>
    </div>
  )
}

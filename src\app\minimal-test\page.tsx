// Completely minimal page - no auth context, no imports except React
'use client'

import { useState } from 'react'

export default function MinimalTestPage() {
  const [message, setMessage] = useState('Page loaded successfully!')

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Minimal Test Page</h1>
      <p>{message}</p>
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => setMessage('Button clicked!')}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Test Button
        </button>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Navigation Test:</h3>
        <a href="/test-login" style={{ color: '#007bff', marginRight: '10px' }}>Test Login</a>
        <a href="/login" style={{ color: '#007bff', marginRight: '10px' }}>Regular Login</a>
        <a href="/debug-auth" style={{ color: '#007bff', marginRight: '10px' }}>Debug Auth</a>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f8f9fa', border: '1px solid #dee2e6' }}>
        <h4>Environment Check:</h4>
        <p>SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not Set'}</p>
        <p>SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not Set'}</p>
        <p>Current URL: {typeof window !== 'undefined' ? window.location.href : 'Server'}</p>
      </div>

      <script dangerouslySetInnerHTML={{
        __html: `
          console.log('Minimal test page loaded');
          console.log('Current URL:', window.location.href);
          console.log('Document ready state:', document.readyState);
        `
      }} />
    </div>
  )
}

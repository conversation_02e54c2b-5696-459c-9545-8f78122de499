'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/components/providers/AuthProvider'
import { TrashIcon, CheckIcon } from '@heroicons/react/24/outline'

interface UploadedFile {
  id: string
  filename: string
  original_name: string
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by: string
  created_at: string
}

interface MediaLibraryProps {
  onSelect?: (file: UploadedFile & { url: string }) => void
  selectedFileId?: string
  showDeleteButton?: boolean
  className?: string
}

export default function MediaLibrary({
  onSelect,
  selectedFileId,
  showDeleteButton = false,
  className = ''
}: MediaLibraryProps) {
  const { user } = useAuth()
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      loadFiles()
    }
  }, [user])

  const loadFiles = async () => {
    try {
      const { data, error } = await supabase
        .from('uploaded_files')
        .select('*')
        .eq('uploaded_by', user?.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setFiles(data || [])
    } catch (error) {
      console.error('Error loading files:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteFile = async (file: UploadedFile) => {
    if (!confirm('Are you sure you want to delete this file?')) return

    setDeleting(file.id)
    try {
      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('media')
        .remove([file.file_path])

      if (storageError) throw storageError

      // Delete from database
      const { error: dbError } = await supabase
        .from('uploaded_files')
        .delete()
        .eq('id', file.id)

      if (dbError) throw dbError

      // Update local state
      setFiles(files.filter(f => f.id !== file.id))
    } catch (error) {
      console.error('Error deleting file:', error)
      alert('Failed to delete file')
    } finally {
      setDeleting(null)
    }
  }

  const getFileUrl = (file: UploadedFile) => {
    const { data: { publicUrl } } = supabase.storage
      .from('media')
      .getPublicUrl(file.file_path)
    return publicUrl
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className={`${className} flex justify-center items-center py-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className={`${className} text-center py-8`}>
        <p className="text-gray-500 dark:text-gray-400">No files uploaded yet.</p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {files.map((file) => {
          const url = getFileUrl(file)
          const isSelected = selectedFileId === file.id
          
          return (
            <div
              key={file.id}
              className={`
                relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all
                ${isSelected 
                  ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' 
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }
              `}
              onClick={() => onSelect?.({ ...file, url })}
            >
              {/* Image */}
              <div className="aspect-square bg-gray-100 dark:bg-gray-800">
                <img
                  src={url}
                  alt={file.original_name}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>

              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all">
                {/* Selected indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                    <CheckIcon className="h-4 w-4" />
                  </div>
                )}

                {/* Delete button */}
                {showDeleteButton && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteFile(file)
                    }}
                    disabled={deleting === file.id}
                    className="absolute top-2 left-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600 disabled:opacity-50"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>

              {/* File info */}
              <div className="p-2 bg-white dark:bg-gray-800">
                <p className="text-xs text-gray-600 dark:text-gray-400 truncate" title={file.original_name}>
                  {file.original_name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {formatFileSize(file.file_size)}
                </p>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

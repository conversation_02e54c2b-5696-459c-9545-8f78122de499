import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  // Skip middleware for certain paths
  const skipPaths = ['/standalone-login', '/simple-login', '/minimal-test', '/debug-auth', '/test-login', '/clear-session', '/test-basic', '/dashboard/test-upload', '/debug-session']
  if (skipPaths.some(path => req.nextUrl.pathname.startsWith(path))) {
    console.log('Middleware - Skipping path:', req.nextUrl.pathname)
    return NextResponse.next()
  }
  let response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return req.cookies.get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            req.cookies.set({
              name,
              value,
              ...options,
            })
            response = NextResponse.next({
              request: {
                headers: req.headers,
              },
            })
            response.cookies.set({
              name,
              value,
              ...options,
            })
          },
          remove(name: string, options: CookieOptions) {
            req.cookies.set({
              name,
              value: '',
              ...options,
            })
            response = NextResponse.next({
              request: {
                headers: req.headers,
              },
            })
            response.cookies.set({
              name,
              value: '',
              ...options,
            })
          },
        },
      }
    )

    // Refresh session if expired - required for Server Components
    const {
      data: { session },
    } = await supabase.auth.getSession()

    console.log('Middleware - Path:', req.nextUrl.pathname, 'Session:', !!session)

    // Protected routes that require authentication
    const protectedRoutes = ['/dashboard']
    const isProtectedRoute = protectedRoutes.some(route =>
      req.nextUrl.pathname.startsWith(route)
    )

    // If accessing a protected route without authentication, redirect to login
    if (isProtectedRoute && !session) {
      console.log('Redirecting to login - no session for protected route')
      const redirectUrl = new URL('/login', req.url)
      redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname)
      return NextResponse.redirect(redirectUrl)
    }

    // Be more careful about redirecting from login page
    if (req.nextUrl.pathname === '/login' && session) {
      // Only redirect if we have a valid, non-expired session
      const now = Math.floor(Date.now() / 1000)
      if (session.expires_at && session.expires_at > now) {
        console.log('Redirecting from login - user already authenticated')
        const redirectTo = req.nextUrl.searchParams.get('redirectTo') || '/dashboard'
        return NextResponse.redirect(new URL(redirectTo, req.url))
      }
    }

    return response

  } catch (error) {
    console.error('Middleware error:', error)
    return response
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes
     * - debug/test pages
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api|debug-auth|test-login).*)',
  ],
}

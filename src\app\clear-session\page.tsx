'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function ClearSessionPage() {
  const [cleared, setCleared] = useState(false)

  const clearEverything = async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut()
      
      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.clear()
        sessionStorage.clear()
        
        // Clear all cookies
        document.cookie.split(";").forEach((c) => {
          const eqPos = c.indexOf("=")
          const name = eqPos > -1 ? c.substr(0, eqPos) : c
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
        })
      }
      
      setCleared(true)
    } catch (error) {
      console.error('Error clearing session:', error)
    }
  }

  useEffect(() => {
    clearEverything()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-8">
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Session Cleared
        </h1>
        
        {cleared ? (
          <div className="space-y-4">
            <p className="text-green-600 dark:text-green-400">
              ✅ All sessions, cookies, and storage cleared!
            </p>
            
            <div className="space-y-2">
              <a 
                href="/test-login" 
                className="block w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Go to Test Login
              </a>
              
              <a 
                href="/signup" 
                className="block w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Go to Signup
              </a>
              
              <a 
                href="/login" 
                className="block w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Go to Login
              </a>
              
              <a 
                href="/debug-auth" 
                className="block w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Debug Auth State
              </a>
            </div>
          </div>
        ) : (
          <p className="text-gray-600 dark:text-gray-400">
            Clearing sessions and storage...
          </p>
        )}
      </div>
    </div>
  )
}

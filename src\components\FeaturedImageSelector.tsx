'use client'

import { useState } from 'react'
import { PhotoIcon, LinkIcon, FolderIcon } from '@heroicons/react/24/outline'
import MediaUpload from './MediaUpload'
import MediaLibrary from './MediaLibrary'

interface FeaturedImageSelectorProps {
  value: string
  onChange: (url: string) => void
  className?: string
}

type SelectionMode = 'url' | 'upload' | 'library'

interface UploadedFile {
  id: string
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: string
}

export default function FeaturedImageSelector({
  value,
  onChange,
  className = ''
}: FeaturedImageSelectorProps) {
  const [mode, setMode] = useState<SelectionMode>('url')
  const [selectedFileId, setSelectedFileId] = useState<string>('')
  const [uploadError, setUploadError] = useState<string>('')

  const handleUploadComplete = (file: UploadedFile) => {
    onChange(file.url)
    setUploadError('')
  }

  const handleUploadError = (error: string) => {
    setUploadError(error)
  }

  const handleLibrarySelect = (file: UploadedFile & { url: string }) => {
    setSelectedFileId(file.id)
    onChange(file.url)
  }

  const handleUrlChange = (url: string) => {
    onChange(url)
    setSelectedFileId('')
  }

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        Featured Image
      </label>

      {/* Mode Selection */}
      <div className="flex space-x-1 mb-4 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        <button
          type="button"
          onClick={() => setMode('url')}
          className={`
            flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${mode === 'url'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }
          `}
        >
          <LinkIcon className="h-4 w-4 mr-2" />
          URL
        </button>
        <button
          type="button"
          onClick={() => setMode('upload')}
          className={`
            flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${mode === 'upload'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }
          `}
        >
          <PhotoIcon className="h-4 w-4 mr-2" />
          Upload
        </button>
        <button
          type="button"
          onClick={() => setMode('library')}
          className={`
            flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${mode === 'library'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }
          `}
        >
          <FolderIcon className="h-4 w-4 mr-2" />
          Library
        </button>
      </div>

      {/* Content based on mode */}
      {mode === 'url' && (
        <div>
          <input
            type="url"
            value={value}
            onChange={(e) => handleUrlChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="https://example.com/image.jpg"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Enter the URL of an image from the web.
          </p>
        </div>
      )}

      {mode === 'upload' && (
        <div>
          <MediaUpload
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
          />
          {uploadError && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-2">{uploadError}</p>
          )}
        </div>
      )}

      {mode === 'library' && (
        <div className="max-h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-4">
          <MediaLibrary
            onSelect={handleLibrarySelect}
            selectedFileId={selectedFileId}
          />
        </div>
      )}

      {/* Preview */}
      {value && (
        <div className="mt-4">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Preview:</p>
          <div className="relative w-full max-w-md">
            <img
              src={value}
              alt="Featured image preview"
              className="w-full h-48 object-cover rounded-md border border-gray-300 dark:border-gray-600"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

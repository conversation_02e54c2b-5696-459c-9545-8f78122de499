'use client'

import { useAuth } from '@/components/providers/AuthProvider'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [debugInfo, setDebugInfo] = useState('')

  useEffect(() => {
    const checkAuth = async () => {
      // Double-check with Supabase directly
      const { data: { session }, error } = await supabase.auth.getSession()

      setDebugInfo(`AuthProvider - User: ${!!user}, Loading: ${loading}, Session: ${!!session}`)

      // Only redirect if we're sure there's no authentication
      if (!loading && !user && !session) {
        console.log('Dashboard: No authentication found, redirecting to login')
        setIsRedirecting(true)
        window.location.href = '/login'
      }
    }

    // Give AuthProvider some time to load
    const timer = setTimeout(checkAuth, 1000)

    return () => clearTimeout(timer)
  }, [user, loading])

  // Show loading state
  if (loading || isRedirecting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            {isRedirecting ? 'Redirecting to login...' : 'Loading dashboard...'}
          </p>
          <p className="mt-2 text-xs text-gray-500">{debugInfo}</p>
        </div>
      </div>
    )
  }

  // If no user after loading, show a fallback
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Authentication Required
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Please log in to access the dashboard.
          </p>
          <a
            href="/login"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Go to Login
          </a>
          <p className="mt-4 text-xs text-gray-500">{debugInfo}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Welcome back, {user.email}!
            </p>
          </div>

          <button
            onClick={async () => {
              await supabase.auth.signOut()
              window.location.href = '/'
            }}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Sign Out
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          {children}
        </div>
      </div>
    </div>
  )
}

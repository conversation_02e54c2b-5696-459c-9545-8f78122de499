import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]

    // Create Supabase client with the user's token
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      }
    )

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token' },
        { status: 401 }
      )
    }

    // Test storage access
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
    
    // Test creating a simple text file
    const testContent = `Test file created at ${new Date().toISOString()}`
    const testPath = `test-${Date.now()}.txt`
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('media')
      .upload(testPath, testContent, {
        contentType: 'text/plain'
      })

    // Clean up test file
    if (uploadData) {
      await supabase.storage.from('media').remove([testPath])
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email
      },
      storage: {
        buckets: buckets?.map(b => ({ id: b.id, name: b.name, public: b.public })),
        bucketsError: bucketsError?.message,
        testUpload: uploadData ? 'SUCCESS' : 'FAILED',
        uploadError: uploadError?.message
      }
    })

  } catch (error: any) {
    console.error('Storage test error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

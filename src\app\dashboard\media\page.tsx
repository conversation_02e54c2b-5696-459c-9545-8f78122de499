'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { supabase } from '@/lib/supabase'
import { CloudArrowUpIcon } from '@heroicons/react/24/outline'

interface UploadedFile {
  id: string
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: string
}

export default function MediaPage() {
  const { user } = useAuth()
  const [uploadSuccess, setUploadSuccess] = useState<string>('')
  const [uploadError, setUploadError] = useState<string>('')
  const [refreshKey, setRefreshKey] = useState(0)
  const [debugInfo, setDebugInfo] = useState<string>('')

  const handleUploadComplete = (file: UploadedFile) => {
    setUploadSuccess(`Successfully uploaded ${file.originalName}`)
    setUploadError('')
    // Trigger MediaLibrary refresh
    setRefreshKey(prev => prev + 1)
    
    // Clear success message after 3 seconds
    setTimeout(() => setUploadSuccess(''), 3000)
  }

  const handleUploadError = (error: string) => {
    setUploadError(error)
    setUploadSuccess('')
  }

  const testAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setDebugInfo('No session found')
        return
      }

      const response = await fetch('/api/debug/auth', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      const result = await response.json()
      setDebugInfo(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setDebugInfo(`Error: ${error.message}`)
    }
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Media Library</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Upload and manage your images and files.
        </p>
      </div>

      {/* Upload Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <CloudArrowUpIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Upload New File</h2>
        </div>

        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
          <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Upload functionality temporarily simplified for debugging</p>
          <input
            type="file"
            accept="image/*"
            onChange={async (e) => {
              const file = e.target.files?.[0]
              if (file) {
                try {
                  const { data: { session } } = await supabase.auth.getSession()
                  if (!session) {
                    setUploadError('Not authenticated')
                    return
                  }

                  const formData = new FormData()
                  formData.append('file', file)

                  const response = await fetch('/api/upload', {
                    method: 'POST',
                    headers: {
                      'Authorization': `Bearer ${session.access_token}`
                    },
                    body: formData,
                  })

                  const result = await response.json()

                  if (response.ok) {
                    setUploadSuccess(`Uploaded: ${result.file.originalName}`)
                    setUploadError('')
                  } else {
                    setUploadError(result.error || 'Upload failed')
                  }
                } catch (error: any) {
                  setUploadError(error.message)
                }
              }
            }}
            className="mt-4"
          />
        </div>

        {/* Upload Messages */}
        {uploadSuccess && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
            <p className="text-sm text-green-800 dark:text-green-200">{uploadSuccess}</p>
          </div>
        )}

        {uploadError && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
            <p className="text-sm text-red-800 dark:text-red-200">{uploadError}</p>
          </div>
        )}

        {/* Debug Section */}
        <div className="flex gap-2">
          <button
            onClick={testAuth}
            className="px-3 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
          >
            Test Auth
          </button>
        </div>

        {debugInfo && (
          <div className="bg-gray-50 dark:bg-gray-900 border rounded-md p-3">
            <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-auto">{debugInfo}</pre>
          </div>
        )}
      </div>

      {/* Media Library */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Your Files</h2>

        <div className="min-h-96 flex items-center justify-center text-gray-500 dark:text-gray-400">
          <p>Media library temporarily disabled for debugging. Upload functionality is available above.</p>
        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Usage Guidelines</h3>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>• Supported formats: JPEG, PNG, GIF, WebP, SVG</li>
          <li>• Maximum file size: 10MB</li>
          <li>• Recommended dimensions for featured images: 1200x630px</li>
          <li>• Files are automatically optimized for web delivery</li>
          <li>• Deleted files cannot be recovered</li>
        </ul>
      </div>
    </div>
  )
}

export default function TestBasicPage() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f3f4f6', 
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{ 
        maxWidth: '400px', 
        backgroundColor: 'white', 
        padding: '30px', 
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        textAlign: 'center'
      }}>
        <h1 style={{ marginBottom: '20px', color: '#1f2937' }}>
          Basic Test Page
        </h1>
        <p style={{ marginBottom: '20px', color: '#6b7280' }}>
          This page loads without any dependencies to test basic routing.
        </p>
        
        <div style={{ marginTop: '20px' }}>
          <a 
            href="/standalone-login" 
            style={{ 
              display: 'inline-block',
              padding: '10px 20px',
              backgroundColor: '#3b82f6',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              margin: '5px'
            }}
          >
            Go to Standalone Login
          </a>
          
          <a 
            href="/dashboard" 
            style={{ 
              display: 'inline-block',
              padding: '10px 20px',
              backgroundColor: '#10b981',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              margin: '5px'
            }}
          >
            Go to Dashboard
          </a>
        </div>
        
        <div style={{ marginTop: '20px', fontSize: '14px', color: '#6b7280' }}>
          <p>Current URL: {typeof window !== 'undefined' ? window.location.href : 'Server'}</p>
          <p>Time: {new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    </div>
  )
}

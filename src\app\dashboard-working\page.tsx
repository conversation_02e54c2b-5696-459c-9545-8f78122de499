'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export default function WorkingDashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [stats, setStats] = useState({
    totalPosts: 0,
    publishedPosts: 0,
    totalProjects: 0,
    publishedProjects: 0
  })

  useEffect(() => {
    checkAuth()
  }, [])

  useEffect(() => {
    if (user) {
      loadStats()
    }
  }, [user])

  const checkAuth = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error) {
        setError(error.message)
        setLoading(false)
        return
      }

      if (!session) {
        // No session, redirect to login
        window.location.href = '/simple-login'
        return
      }

      setUser(session.user)
      setLoading(false)
    } catch (err: any) {
      setError(err.message)
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Get blog posts stats
      const { data: allPosts } = await supabase
        .from('blog_posts')
        .select('id, published')
        .eq('author_id', user?.id)

      const { data: publishedPosts } = await supabase
        .from('blog_posts')
        .select('id')
        .eq('author_id', user?.id)
        .eq('published', true)

      // Get projects stats
      const { data: allProjects } = await supabase
        .from('projects')
        .select('id, published')
        .eq('author_id', user?.id)

      const { data: publishedProjects } = await supabase
        .from('projects')
        .select('id')
        .eq('author_id', user?.id)
        .eq('published', true)

      setStats({
        totalPosts: allPosts?.length || 0,
        publishedPosts: publishedPosts?.length || 0,
        totalProjects: allProjects?.length || 0,
        publishedProjects: publishedProjects?.length || 0
      })
    } catch (err: any) {
      console.error('Error loading stats:', err)
    }
  }

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      window.location.href = '/'
    } catch (err: any) {
      setError(err.message)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <a 
            href="/simple-login"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Go to Login
          </a>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Not Authenticated
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Please log in to access the dashboard.
          </p>
          <a 
            href="/simple-login"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Go to Login
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Welcome back, {user.email}!
            </p>
          </div>
          
          <button
            onClick={handleSignOut}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Sign Out
          </button>
        </div>

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Blog Posts Stats */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <span className="text-2xl">📝</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Blog Posts
                </h3>
                <p className="text-2xl font-bold text-blue-600">{stats.totalPosts}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {stats.publishedPosts} published
                </p>
              </div>
            </div>
          </div>

          {/* Projects Stats */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <span className="text-2xl">🚀</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Projects
                </h3>
                <p className="text-2xl font-bold text-green-600">{stats.totalProjects}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {stats.publishedProjects} published
                </p>
              </div>
            </div>
          </div>

          {/* User Info */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <span className="text-2xl">👤</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Profile
                </h3>
                <p className="text-sm font-medium text-purple-600">{user.email}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {user.user_metadata?.full_name || 'Name not set'}
                </p>
              </div>
            </div>
          </div>

          {/* Status */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Status
                </h3>
                <p className="text-sm font-medium text-green-600">All Systems</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Operational
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/dashboard/posts/new"
              className="flex items-center justify-center px-6 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <span className="text-xl mr-3">📝</span>
              <div>
                <div className="font-medium">Create New Post</div>
                <div className="text-sm opacity-90">Write a blog article</div>
              </div>
            </a>
            <a
              href="/dashboard/projects/new"
              className="flex items-center justify-center px-6 py-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <span className="text-xl mr-3">🚀</span>
              <div>
                <div className="font-medium">Add Project</div>
                <div className="text-sm opacity-90">Showcase your work</div>
              </div>
            </a>
            <a
              href="/dashboard/settings"
              className="flex items-center justify-center px-6 py-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <span className="text-xl mr-3">⚙️</span>
              <div>
                <div className="font-medium">Settings</div>
                <div className="text-sm opacity-90">Update your profile</div>
              </div>
            </a>
          </div>
        </div>

        {/* Content Management */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Content Management
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Blog Posts Section */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                📝 Blog Posts
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Create and manage your blog articles
              </p>
              <div className="space-y-2">
                <a
                  href="/dashboard/posts"
                  className="block w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-center"
                >
                  Manage Posts
                </a>
                <a
                  href="/dashboard/posts/new"
                  className="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors text-center"
                >
                  Create New Post
                </a>
              </div>
            </div>

            {/* Projects Section */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                🚀 Projects
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Showcase your portfolio projects
              </p>
              <div className="space-y-2">
                <a
                  href="/dashboard/projects"
                  className="block w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors text-center"
                >
                  Manage Projects
                </a>
                <a
                  href="/dashboard/projects/new"
                  className="block w-full px-4 py-2 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors text-center"
                >
                  Create New Project
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Navigation
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a
              href="/dashboard/settings"
              className="block text-center px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
            >
              ⚙️ Settings
            </a>
            <a
              href="/blog"
              className="block text-center px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
            >
              📖 View Blog
            </a>
            <a
              href="/projects"
              className="block text-center px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700 transition-colors"
            >
              💼 View Projects
            </a>
            <a
              href="/"
              className="block text-center px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              🏠 Home
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

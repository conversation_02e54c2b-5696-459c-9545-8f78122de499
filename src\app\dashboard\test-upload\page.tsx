'use client'

import { useState } from 'react'

export default function TestUploadPage() {
  const [message, setMessage] = useState('')

  const testUpload = async () => {
    setMessage('Testing upload functionality...')
    
    // Simple test without any complex components
    try {
      const response = await fetch('/api/debug/auth', {
        headers: {
          'Authorization': 'Bearer test'
        }
      })
      
      const result = await response.json()
      setMessage(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Test Upload</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Simple test page for upload functionality.
        </p>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <button
          onClick={testUpload}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Test API
        </button>
        
        {message && (
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded">
            <pre className="text-sm">{message}</pre>
          </div>
        )}
      </div>
    </div>
  )
}

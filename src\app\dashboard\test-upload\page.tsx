'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestUploadPage() {
  const [message, setMessage] = useState('')

  const testAuth = async () => {
    setMessage('Testing authentication...')

    try {
      // Get the real session token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('No session found - please log in first')
        return
      }

      const response = await fetch('/api/debug/auth', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      const result = await response.json()
      setMessage(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    }
  }

  const testUpload = async () => {
    setMessage('Testing file upload...')

    try {
      // Get the real session token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('No session found - please log in first')
        return
      }

      // Create a simple test file
      const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' })
      const formData = new FormData()
      formData.append('file', testFile)

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: formData
      })

      const result = await response.json()
      setMessage(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Test Upload</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Simple test page for upload functionality.
        </p>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="space-x-4">
          <button
            onClick={testAuth}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Auth
          </button>

          <button
            onClick={testUpload}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Upload
          </button>
        </div>

        {message && (
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded">
            <pre className="text-sm overflow-auto">{message}</pre>
          </div>
        )}
      </div>
    </div>
  )
}

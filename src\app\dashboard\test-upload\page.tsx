'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestUploadPage() {
  const [message, setMessage] = useState('')

  const testAuth = async () => {
    setMessage('Testing authentication...')

    try {
      // Get the real session token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('No session found - please log in first')
        return
      }

      const response = await fetch('/api/debug/auth', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      const result = await response.json()
      setMessage(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    }
  }

  const testStorage = async () => {
    setMessage('Testing storage access...')

    try {
      // Get the real session token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('No session found - please log in first')
        return
      }

      const response = await fetch('/api/test-storage', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      const result = await response.json()
      setMessage(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    }
  }

  const testUpload = async (file?: File) => {
    if (!file) {
      setMessage('Please select an image file to upload')
      return
    }

    setMessage('Testing file upload...')

    try {
      // Get the real session token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setMessage('No session found - please log in first')
        return
      }

      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: formData
      })

      const result = await response.json()
      setMessage(JSON.stringify(result, null, 2))
    } catch (error: any) {
      setMessage(`Error: ${error.message}`)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Test Upload</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Simple test page for upload functionality.
        </p>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="space-y-4">
          <div className="space-x-4">
            <button
              onClick={testAuth}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Test Auth
            </button>

            <button
              onClick={testStorage}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Test Storage
            </button>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Upload Image File:</label>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) {
                  testUpload(file)
                }
              }}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
            />
          </div>
        </div>

        {message && (
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded">
            <pre className="text-sm overflow-auto">{message}</pre>
          </div>
        )}
      </div>
    </div>
  )
}

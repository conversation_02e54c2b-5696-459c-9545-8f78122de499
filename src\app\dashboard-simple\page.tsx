'use client'

import { useAuth } from '@/components/providers/AuthProvider'

export default function SimpleDashboardPage() {
  const { user, loading } = useAuth()

  console.log('SimpleDashboard - User:', user)
  console.log('SimpleDashboard - Loading:', loading)

  if (loading) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Loading...</h1>
        <p>Checking authentication...</p>
      </div>
    )
  }

  if (!user) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Not Authenticated</h1>
        <p>Please log in first.</p>
        <a href="/standalone-login" style={{ color: 'blue' }}>Go to Login</a>
      </div>
    )
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Simple Dashboard</h1>
      <p>Welcome, {user.email}!</p>
      <p>User ID: {user.id}</p>
      <p>Full Name: {user.user_metadata?.full_name || 'Not set'}</p>
      
      <div style={{ marginTop: '20px' }}>
        <h2>Navigation Test:</h2>
        <a href="/dashboard" style={{ color: 'blue', marginRight: '10px' }}>Full Dashboard</a>
        <a href="/dashboard/posts" style={{ color: 'blue', marginRight: '10px' }}>Posts</a>
        <a href="/dashboard/settings" style={{ color: 'blue', marginRight: '10px' }}>Settings</a>
      </div>
    </div>
  )
}
